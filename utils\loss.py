import torch
import torch.nn as nn
import torch.nn.functional as F
import torchvision.models as models
from pytorch_msssim import ssim

# 定义损失函数
class FusionLoss(nn.Module):
    def __init__(self, lambda_rad=0.1, lambda_perc=0.1, lambda_struct=0.1):
        super(FusionLoss, self).__init__()
        self.mse_loss = nn.MSELoss()
        self.l1_loss = nn.L1Loss()

        # 权重系数
        self.lambda_rad = lambda_rad  # 辐射一致性损失权重
        self.lambda_perc = lambda_perc  # 感知损失权重
        self.lambda_struct = lambda_struct  # 结构损失权重

        # 初始化VGG网络用于感知损失
        vgg = models.vgg19(pretrained=True).features
        self.vgg_features = nn.Sequential()
        for i in range(36):  # 使用VGG前36层
            self.vgg_features.add_module(str(i), vgg[i])

        # 冻结VGG参数
        for param in self.vgg_features.parameters():
            param.requires_grad = False

    def radiation_consistency_loss(self, output, target):
        # 计算降采样后的输出与原始低分辨率图像之间的L1距离

        return self.l1_loss(output, target)

    def perceptual_loss(self, output, target):
        # 确保输入是3通道的
        if output.size(1) == 1:
            output = output.repeat(1, 3, 1, 1)
        if target.size(1) == 1:
            target = target.repeat(1, 3, 1, 1)

        # 提取特征
        output_features = self.vgg_features(output)
        target_features = self.vgg_features(target)

        # 计算特征图之间的MSE损失
        return self.mse_loss(output_features, target_features)


# 然后在structural_similarity_loss中:
    def structural_similarity_loss(self, output, target):
        return 1.0 - ssim(output, target, data_range=1.0, size_average=True)


    def forward(self, output, target):
        """
        计算总损失
        output: 模型输出的融合图像
        target: 目标高分辨率NTL图像
        """
        # 确保VGG在与输入相同的设备上
        if not next(self.vgg_features.parameters()).device == output.device:
            self.vgg_features = self.vgg_features.to(output.device)

        # 新增损失
        radiation_loss = self.radiation_consistency_loss(output, target)
        perceptual_loss = self.perceptual_loss(output, target)
        ssim_loss = self.structural_similarity_loss(output, target)

        # 总损失
        total_loss = (
                self.lambda_rad * radiation_loss +
                self.lambda_perc * perceptual_loss +
                self.lambda_struct * ssim_loss
        )

        return total_loss
