{"cells": [{"cell_type": "code", "execution_count": 15, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "cuQA96FcoraD", "outputId": "4d70f8c0-05fa-40a1-b617-ee32a4238c4e", "executionInfo": {"status": "ok", "timestamp": 1749027798133, "user_tz": -480, "elapsed": 342039, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["2025-06-04 08:28:06,200 - INFO - 训练配置: {'batch_size': 16, 'epochs': 20, 'lr': 0.0005, 'd_lr': 2e-05, 'weight_decay': 1e-06, 'scheduler': 'plateau', 'lr_patience': 3, 'lr_factor': 0.5, 'min_lr': 1e-07, 'MS_data_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/train/MS', 'NTL_data_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/train/NTL', 'save_dir': '/content/drive/MyDrive/d2l-zh/SRGAN/data/model', 'up_scale': 4, 'cuda': True, 'lambda_rad': 1, 'lambda_perc': 2, 'lambda_struct': 1, 'lambda_adv': 0.1, 'lambda_cycle': 0.1, 'lambda_identity': 1.0, 'save_interval': 10, 'val_ratio': 0.1, 'seed': 42, 'n_critic': 2, 'warmup_epochs': 5}\n", "2025-06-04 08:28:06,200 - INFO - 使用设备: cuda\n", "2025-06-04 08:28:06,632 - INFO - G_LR_to_HR参数数量: 1039539\n", "2025-06-04 08:28:06,632 - INFO - G_HR_to_LR参数数量: 744276\n", "2025-06-04 08:28:06,633 - INFO - D_HR参数数量: 1555329\n", "2025-06-04 08:28:06,633 - INFO - D_LR参数数量: 1555329\n", "/usr/local/lib/python3.11/dist-packages/torch/optim/lr_scheduler.py:62: UserWarning: The verbose parameter is deprecated. Please use get_last_lr() to access the learning rate.\n", "  warnings.warn(\n", "2025-06-04 08:28:06,634 - INFO - 使用ReduceLROnPlateau学习率调度器 (因子: 0.5, 耐心值: 3)\n", "/usr/local/lib/python3.11/dist-packages/torchvision/models/_utils.py:208: UserWarning: The parameter 'pretrained' is deprecated since 0.13 and may be removed in the future, please use 'weights' instead.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/torchvision/models/_utils.py:223: UserWarning: Arguments other than a weight enum or `None` for 'weights' are deprecated since 0.13 and may be removed in the future. The current behavior is equivalent to passing `weights=VGG19_Weights.IMAGENET1K_V1`. You can also use `weights=VGG19_Weights.DEFAULT` to get the most up-to-date weights.\n", "  warnings.warn(msg)\n", "No blur will be applied to NTL HR before downsampling.\n", "2025-06-04 08:28:08,311 - INFO - 数据集总大小: 1600\n", "2025-06-04 08:28:08,311 - INFO - 训练集大小: 1440\n", "2025-06-04 08:28:08,311 - INFO - 验证集大小: 160\n", "/usr/local/lib/python3.11/dist-packages/torch/utils/data/dataloader.py:624: UserWarning: This DataLoader will create 4 worker processes in total. Our suggested max number of worker in current system is 2, which is smaller than what this DataLoader is going to create. Please be aware that excessive worker creation might get DataLoader running slow or even freeze, lower the worker number to avoid potential slowness/freeze if necessary.\n", "  warnings.warn(\n", "2025-06-04 08:29:55,629 - INFO - Epoch [1/20], G训练: 0.0012, G验证: 0.0010, D_HR训练: 0.0000, D_HR验证: 0.0000, D_LR训练: 0.0000, D_LR验证: 0.0000, 循环损失: 0.0002,  PSNR: 59.53dB, SSIM: 0.9946,mixed_score：0.9976 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:47.315201\n", "2025-06-04 08:29:55,799 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-04 08:31:42,242 - INFO - Epoch [2/20], G训练: 0.0010, G验证: 0.0010, D_HR训练: 0.0000, D_HR验证: 0.0000, D_LR训练: 0.0000, D_LR验证: 0.0000, 循环损失: 0.0002,  PSNR: 59.78dB, SSIM: 0.9948,mixed_score：0.9977 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:46.441510\n", "2025-06-04 08:31:42,417 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-04 08:33:27,333 - INFO - Epoch [3/20], G训练: 0.0010, G验证: 0.0009, D_HR训练: 0.0000, D_HR验证: 0.0000, D_LR训练: 0.0000, D_LR验证: 0.0000, 循环损失: 0.0002,  PSNR: 59.97dB, SSIM: 0.9950,mixed_score：0.9978 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:44.910824\n", "2025-06-04 08:33:27,506 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-04 08:35:10,670 - INFO - Epoch [4/20], G训练: 0.0010, G验证: 0.0010, D_HR训练: 0.0000, D_HR验证: 0.0000, D_LR训练: 0.0000, D_LR验证: 0.0000, 循环损失: 0.0002,  PSNR: 59.46dB, SSIM: 0.9947,mixed_score：0.9976 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:43.159036\n", "2025-06-04 08:36:52,251 - INFO - Epoch [5/20], G训练: 0.0010, G验证: 0.0009, D_HR训练: 0.0000, D_HR验证: 0.0000, D_LR训练: 0.0000, D_LR验证: 0.0000, 循环损失: 0.0002,  PSNR: 60.02dB, SSIM: 0.9952,mixed_score：0.9978 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:41.577520\n", "2025-06-04 08:36:52,419 - INFO - 保存最佳模型到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth\n", "2025-06-04 08:38:27,786 - INFO - Epoch [6/20], G训练: 0.1978, G验证: 0.1836, D_HR训练: 0.7124, D_HR验证: 0.6996, D_LR训练: 0.6765, D_LR验证: 0.7152, 循环损失: 0.0003,  PSNR: 59.53dB, SSIM: 0.9948,mixed_score：0.9977 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:35.360803\n", "2025-06-04 08:40:16,290 - INFO - Epoch [7/20], G训练: 0.2068, G验证: 0.2087, D_HR训练: 0.6730, D_HR验证: 0.6944, D_LR训练: 0.6957, D_LR验证: 0.5609, 循环损失: 0.0003,  PSNR: 57.86dB, SSIM: 0.9942,mixed_score：0.9974 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:48.502473\n", "2025-06-04 08:42:05,163 - INFO - Epoch [8/20], G训练: 0.2235, G验证: 0.1786, D_HR训练: 0.6362, D_HR验证: 0.6991, D_LR训练: 0.6862, D_LR验证: 0.6677, 循环损失: 0.0003,  PSNR: 59.47dB, SSIM: 0.9948,mixed_score：0.9977 学习率G: 5.00e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:48.870930\n", "2025-06-04 08:43:57,421 - INFO - Epoch [9/20], G训练: 0.2490, G验证: 0.3618, D_HR训练: 0.6336, D_HR验证: 0.5600, D_LR训练: 0.6479, D_LR验证: 0.5126, 循环损失: 0.0003,  PSNR: 58.12dB, SSIM: 0.9924,mixed_score：0.9966 学习率G: 2.50e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:52.256821\n", "2025-06-04 08:45:42,049 - INFO - Epoch [10/20], G训练: 0.2288, G验证: 0.1760, D_HR训练: 0.5676, D_HR验证: 0.5639, D_LR训练: 0.6816, D_LR验证: 0.6460, 循环损失: 0.0002,  PSNR: 59.13dB, SSIM: 0.9942,mixed_score：0.9974 学习率G: 2.50e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:44.624463\n", "2025-06-04 08:47:28,376 - INFO - Epoch [11/20], G训练: 0.2337, G验证: 0.2811, D_HR训练: 0.5258, D_HR验证: 0.6131, D_LR训练: 0.6798, D_LR验证: 0.5438, 循环损失: 0.0003,  PSNR: 50.08dB, SSIM: 0.9774,mixed_score：0.9898 学习率G: 2.50e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:46.325417\n", "2025-06-04 08:49:12,025 - INFO - Epoch [12/20], G训练: 0.1670, G验证: 0.1810, D_HR训练: 0.6788, D_HR验证: 0.6844, D_LR训练: 0.6910, D_LR验证: 0.7142, 循环损失: 0.0003,  PSNR: 55.80dB, SSIM: 0.9922,mixed_score：0.9965 学习率G: 2.50e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 2.00e-05, 时间: 0:01:43.646840\n", "2025-06-04 08:50:56,505 - INFO - Epoch [13/20], G训练: 0.2006, G验证: 0.1792, D_HR训练: 0.6041, D_HR验证: 0.5402, D_LR训练: 0.6835, D_LR验证: 0.6928, 循环损失: 0.0003,  PSNR: 58.96dB, SSIM: 0.9943,mixed_score：0.9974 学习率G: 1.25e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 1.00e-05, 时间: 0:01:44.478240\n", "2025-06-04 08:52:45,051 - INFO - Epoch [14/20], G训练: 0.1958, G验证: 0.2236, D_HR训练: 0.5438, D_HR验证: 0.4747, D_LR训练: 0.6725, D_LR验证: 0.6827, 循环损失: 0.0002,  PSNR: 56.94dB, SSIM: 0.9890,mixed_score：0.9950 学习率G: 1.25e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 1.00e-05, 时间: 0:01:48.544270\n", "2025-06-04 08:54:30,461 - INFO - Epoch [15/20], G训练: 0.2126, G验证: 0.2014, D_HR训练: 0.5534, D_HR验证: 0.3852, D_LR训练: 0.6757, D_LR验证: 0.6775, 循环损失: 0.0003,  PSNR: 51.79dB, SSIM: 0.9709,mixed_score：0.9869 学习率G: 1.25e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 1.00e-05, 时间: 0:01:45.407676\n", "2025-06-04 08:56:12,964 - INFO - Epoch [16/20], G训练: 0.2180, G验证: 0.2423, D_HR训练: 0.5751, D_HR验证: 0.4678, D_LR训练: 0.6669, D_LR验证: 0.7286, 循环损失: 0.0003,  PSNR: 56.54dB, SSIM: 0.9908,mixed_score：0.9958 学习率G: 1.25e-04, 学习率D_HR: 2.00e-05, 学习率D_LR: 1.00e-05, 时间: 0:01:42.501101\n", "2025-06-04 08:58:01,422 - INFO - Epoch [17/20], G训练: 0.2355, G验证: 0.2188, D_HR训练: 0.4208, D_HR验证: 0.3486, D_LR训练: 0.6676, D_LR验证: 0.6568, 循环损失: 0.0003,  PSNR: 56.99dB, SSIM: 0.9906,mixed_score：0.9958 学习率G: 6.25e-05, 学习率D_HR: 2.00e-05, 学习率D_LR: 5.00e-06, 时间: 0:01:48.454130\n", "2025-06-04 08:59:40,350 - INFO - Epoch [18/20], G训练: 0.2672, G验证: 0.3897, D_HR训练: 0.3336, D_HR验证: 0.4423, D_LR训练: 0.6655, D_LR验证: 0.6581, 循环损失: 0.0003,  PSNR: 57.18dB, SSIM: 0.9868,mixed_score：0.9941 学习率G: 6.25e-05, 学习率D_HR: 2.00e-05, 学习率D_LR: 5.00e-06, 时间: 0:01:38.926540\n", "2025-06-04 09:01:20,370 - INFO - Epoch [19/20], G训练: 0.2633, G验证: 0.3447, D_HR训练: 0.4387, D_HR验证: 0.3913, D_LR训练: 0.6615, D_LR验证: 0.6751, 循环损失: 0.0002,  PSNR: 57.76dB, SSIM: 0.9938,mixed_score：0.9972 学习率G: 6.25e-05, 学习率D_HR: 2.00e-05, 学习率D_LR: 5.00e-06, 时间: 0:01:40.018244\n", "2025-06-04 09:03:02,971 - INFO - Epoch [20/20], G训练: 0.2416, G验证: 0.2571, D_HR训练: 0.5633, D_HR验证: 0.5582, D_LR训练: 0.6597, D_LR验证: 0.6620, 循环损失: 0.0003,  PSNR: 54.40dB, SSIM: 0.9869,mixed_score：0.9941 学习率G: 6.25e-05, 学习率D_HR: 2.00e-05, 学习率D_LR: 5.00e-06, 时间: 0:01:42.599192\n", "2025-06-04 09:03:03,267 - INFO - 训练完成！最终模型保存到: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/final_model.pth\n", "2025-06-04 09:03:07,672 - INFO - Training curves saved to: /content/drive/MyDrive/d2l-zh/SRGAN/data/model/training_curves_20250604_090303.png\n"]}], "source": ["!python /content/drive/MyDrive/d2l-zh/SRGAN/train.py"]}, {"cell_type": "code", "execution_count": 18, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 4863, "status": "ok", "timestamp": 1749028403514, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "3rPuSnZ6orQr", "outputId": "d79e978a-8361-41d8-ae01-733e623ba6de"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Namespace(MS_data_dir='/content/drive/MyDrive/d2l-zh/SRGAN/data/predit/MS', NPP_data_dir='/content/drive/MyDrive/d2l-zh/SRGAN/data/predit/NPP', model='/content/drive/MyDrive/d2l-zh/SRGAN/data/model/best_model.pth', save_folder='/content/drive/MyDrive/d2l-zh/SRGAN/data/out', cuda=True)\n", "使用设备: cuda\n", "成功加载 CycleGAN 模型的 G_LR_to_HR 生成器，并切换到 eval 模式\n", "找到 5 个多光谱图像\n", "找到 5 个NPP图像\n", "处理的图像对数量: 5\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_1.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_2.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_3.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_4.tif\n", "融合完成: /content/drive/MyDrive/d2l-zh/SRGAN/data/out/Fused_MS_NPP_5.tif\n", "收皮\n"]}], "source": ["!python /content/drive/MyDrive/d2l-zh/SRGAN/test.py"]}, {"cell_type": "code", "source": [], "metadata": {"id": "rlAcUai3wR6c"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 7559, "status": "ok", "timestamp": 1749019915423, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "hVZEBNhworA8", "outputId": "6afb7b70-2f96-4f67-ff88-80fcb90c9264"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting rasterio\n", "  Downloading rasterio-1.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.1 kB)\n", "Collecting affine (from rasterio)\n", "  Downloading affine-2.4.0-py3-none-any.whl.metadata (4.0 kB)\n", "Requirement already satisfied: attrs in /usr/local/lib/python3.11/dist-packages (from rasterio) (25.3.0)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from rasterio) (2025.4.26)\n", "Requirement already satisfied: click>=4.0 in /usr/local/lib/python3.11/dist-packages (from rasterio) (8.2.1)\n", "Collecting cligj>=0.5 (from rasterio)\n", "  Downloading cligj-0.7.2-py3-none-any.whl.metadata (5.0 kB)\n", "Requirement already satisfied: numpy>=1.24 in /usr/local/lib/python3.11/dist-packages (from rasterio) (2.0.2)\n", "Collecting click-plugins (from rasterio)\n", "  Downloading click_plugins-1.1.1-py2.py3-none-any.whl.metadata (6.4 kB)\n", "Requirement already satisfied: pyparsing in /usr/local/lib/python3.11/dist-packages (from rasterio) (3.2.3)\n", "Downloading rasterio-1.4.3-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (22.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m22.2/22.2 MB\u001b[0m \u001b[31m57.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading cligj-0.7.2-py3-none-any.whl (7.1 kB)\n", "Downloading affine-2.4.0-py3-none-any.whl (15 kB)\n", "Downloading click_plugins-1.1.1-py2.py3-none-any.whl (7.5 kB)\n", "Installing collected packages: cligj, click-plugins, affine, rasterio\n", "Successfully installed affine-2.4.0 click-plugins-1.1.1 cligj-0.7.2 rasterio-1.4.3\n"]}], "source": ["!pip install rasterio"]}, {"cell_type": "code", "execution_count": null, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 100336, "status": "ok", "timestamp": 1749020015762, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "4lEir2SAPO24", "outputId": "850b16e5-a9c8-459b-af42-c71f2757b7ea"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting pytorch_msssim\n", "  Downloading pytorch_msssim-1.0.0-py3-none-any.whl.metadata (8.0 kB)\n", "Requirement already satisfied: torch in /usr/local/lib/python3.11/dist-packages (from pytorch_msssim) (2.6.0+cu124)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (4.13.2)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (2025.3.2)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==********** (from torch->pytorch_msssim)\n", "  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==******** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch->pytorch_msssim)\n", "  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (12.4.127)\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch->pytorch_msssim)\n", "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch->pytorch_msssim) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch->pytorch_msssim) (1.3.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch->pytorch_msssim) (3.0.2)\n", "Downloading pytorch_msssim-1.0.0-py3-none-any.whl (7.7 kB)\n", "Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m112.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m90.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m57.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m6.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m11.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m8.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m59.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hInstalling collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, pytorch_msssim\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n", "  Attempting uninstall: nvidia-curand-cu12\n", "    Found existing installation: nvidia-curand-cu12 10.3.6.82\n", "    Uninstalling nvidia-curand-cu12-10.3.6.82:\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n", "  Attempting uninstall: nvidia-cufft-cu12\n", "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n", "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cublas-cu12\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n", "  Attempting uninstall: nvidia-cusparse-cu12\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n", "  Attempting uninstall: nvidia-cudnn-cu12\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n", "  Attempting uninstall: nvidia-cusolver-cu12\n", "    Found existing installation: nvidia-cusolver-cu12 *********\n", "    Uninstalling nvidia-cusolver-cu12-*********:\n", "      Successfully uninstalled nvidia-cusolver-cu12-*********\n", "Successfully installed nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.4.127 nvidia-cuda-nvrtc-cu12-12.4.127 nvidia-cuda-runtime-cu12-12.4.127 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-nvjitlink-cu12-12.4.127 pytorch_msssim-1.0.0\n"]}], "source": ["!pip install pytorch_msssim"]}, {"cell_type": "code", "execution_count": 10, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2286, "status": "ok", "timestamp": 1749022301892, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}, "user_tz": -480}, "id": "G2NzqWprZw59", "outputId": "fcbb33fa-aa26-4571-b74a-fb893793146f"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Drive already mounted at /content/drive; to attempt to forcibly remount, call drive.mount(\"/content/drive\", force_remount=True).\n"]}], "source": ["from google.colab import drive\n", "drive.mount('/content/drive')"]}, {"cell_type": "code", "execution_count": 11, "metadata": {"id": "d-wbLVxxj2zv", "executionInfo": {"status": "ok", "timestamp": 1749022460120, "user_tz": -480, "elapsed": 17825, "user": {"displayName": "龙行运", "userId": "08036334184327795280"}}}, "outputs": [], "source": ["import os\n", "\n", "try:\n", "    import gdal\n", "except:\n", "    from osgeo import gdal\n", "import numpy as np\n", "\n", "\n", "# 读取tif数据集\n", "def readTif(fileName):\n", "    dataset = gdal.Open(fileName)\n", "    if dataset is None:\n", "        print(fileName + \"文件无法打开\")\n", "    return dataset\n", "\n", "\n", "# 保存tif文件函数（改进数据类型处理和添加压缩方式）\n", "def writeTiff(im_data, im_geotrans, im_proj, path, datatype, compress='LZW'):\n", "    # 根据输入数据的维度判断波段数\n", "    if len(im_data.shape) == 3:\n", "        im_bands, im_height, im_width = im_data.shape\n", "    elif len(im_data.shape) == 2:\n", "        im_data = np.array([im_data])\n", "        im_bands, im_height, im_width = im_data.shape\n", "\n", "    # 创建文件，使用压缩选项（LZW, DEFLATE等）\n", "    driver = gdal.Get<PERSON><PERSON><PERSON><PERSON><PERSON>(\"GTiff\")\n", "    options = [\"COMPRESS={}\".format(compress)]\n", "    dataset = driver.Create(path, int(im_width), int(im_height), int(im_bands), datatype, options=options)\n", "\n", "    if dataset is not None:\n", "        dataset.SetGeoTransform(im_geotrans)  # 写入仿射变换参数\n", "        dataset.SetProjection(im_proj)  # 写入投影\n", "\n", "    # 写入每个波段\n", "    for i in range(im_bands):\n", "        dataset.GetRasterBand(i + 1).WriteArray(im_data[i])\n", "    del dataset\n", "\n", "\n", "# 像素坐标和地理坐标仿射变换\n", "def CoordTransf(Xpixel, Ypixel, GeoTransform):\n", "    XGeo = GeoTransform[0] + GeoTransform[1] * Xpixel + Ypixel * GeoTransform[2]\n", "    YGeo = GeoTransform[3] + GeoTransform[4] * Xpixel + Ypixel * GeoTransform[5]\n", "    return <PERSON><PERSON><PERSON>, YGeo\n", "\n", "\n", "# 裁剪函数，改进数据类型处理\n", "def TifCrop(Tif<PERSON>ath, SavePath, CropSize, RepetitionRate):\n", "    if not os.path.exists(SavePath):\n", "        os.makedirs(SavePath)\n", "\n", "    dataset_img = readTif(TifPath)\n", "    width = dataset_img.RasterXSize\n", "    height = dataset_img.RasterYSize\n", "    proj = dataset_img.GetProjection()\n", "    geotrans = dataset_img.GetGeoTransform()\n", "    img = dataset_img.ReadAsArray(0, 0, width, height)  # 获取数据\n", "\n", "    # 获取数据类型，保持与原始影像一致\n", "    datatype = dataset_img.GetRasterBand(1).DataType\n", "\n", "    new_name = len(os.listdir(SavePath)) + 1\n", "\n", "    for i in range(int((height - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        for j in range(int((width - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "            # 如果图像是单波段\n", "            if len(img.shape) == 2:\n", "                cropped = img[\n", "                          int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                          int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "            # 如果图像是多波段\n", "            else:\n", "                cropped = img[:,\n", "                          int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                          int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "\n", "            XGeo, YGeo = CoordTransf(int(j * CropSize * (1 - RepetitionRate)),\n", "                                     int(i * CropSize * (1 - RepetitionRate)),\n", "                                     geotrans)\n", "            crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "            writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "            new_name += 1\n", "\n", "    # 向前裁剪最后一列\n", "    for i in range(int((height - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        if len(img.shape) == 2:\n", "            cropped = img[int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                      (width - CropSize): width]\n", "        else:\n", "            cropped = img[:,\n", "                      int(i * CropSize * (1 - RepetitionRate)): int(i * CropSize * (1 - RepetitionRate)) + CropSize,\n", "                      (width - CropSize): width]\n", "\n", "        XGeo, YGeo = CoordTransf(width - CropSize, int(i * CropSize * (1 - RepetitionRate)), geotrans)\n", "        crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "        writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "        new_name += 1\n", "\n", "    # 向前裁剪最后一行\n", "    for j in range(int((width - CropSize * RepetitionRate) / (CropSize * (1 - RepetitionRate)))):\n", "        if len(img.shape) == 2:\n", "            cropped = img[(height - CropSize): height,\n", "                      int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "        else:\n", "            cropped = img[:,\n", "                      (height - CropSize): height,\n", "                      int(j * CropSize * (1 - RepetitionRate)): int(j * CropSize * (1 - RepetitionRate)) + CropSize]\n", "\n", "        XGeo, YGeo = CoordTransf(int(j * CropSize * (1 - RepetitionRate)), height - CropSize, geotrans)\n", "        crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "        writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "        new_name += 1\n", "\n", "    # 裁剪右下角\n", "    if len(img.shape) == 2:\n", "        cropped = img[(height - CropSize): height, (width - CropSize): width]\n", "    else:\n", "        cropped = img[:, (height - CropSize): height, (width - CropSize): width]\n", "\n", "    XGeo, YGeo = CoordTransf(width - CropSize, height - CropSize, geotrans)\n", "    crop_geotrans = (XGeo, geotrans[1], geotrans[2], YGeo, geotrans[4], geotrans[5])\n", "    writeTiff(cropped, crop_geotrans, proj, SavePath + \"/%d.tif\" % new_name, datatype)\n", "    new_name += 1\n", "\n", "\n", "# 将影像1裁剪为重复率为0.1的64×64的数据集\n", "TifCrop(r\"/content/drive/MyDrive/d2l-zh/SRGAN/data/B0.tif\", r\"/content/drive/MyDrive/d2l-zh/SRGAN/data/train/NTL/\", 80, 0.5)\n"]}, {"cell_type": "code", "source": [], "metadata": {"id": "1tpzUV3eYIQx"}, "execution_count": null, "outputs": []}], "metadata": {"accelerator": "GPU", "colab": {"gpuType": "T4", "provenance": []}, "kernelspec": {"display_name": "Python 3", "name": "python3"}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}