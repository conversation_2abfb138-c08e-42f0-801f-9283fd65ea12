import torch
import torch.nn.functional as F
from skimage.metrics import peak_signal_noise_ratio as psnr
import numpy as np
from pytorch_msssim import ssim  # Using pytorch_msssim for SSIM

class ImageMetrics:
    def __init__(self, device):
        self.device = device

    def calculate_psnr(self, pred, target):
        """Calculate PSNR between predicted and target images."""
        pred = pred.detach().cpu().clamp(0, 1).numpy()
        target = target.detach().cpu().clamp(0, 1).numpy()

        psnr_vals = []
        for p, t in zip(pred, target):
            psnr_val = psnr(t, p, data_range=1.0)
            psnr_vals.append(psnr_val)

        return np.mean(psnr_vals)

    def calculate_ssim(self, pred, target):
        """Calculate SSIM between predicted and target images."""
        pred = pred.detach().cpu().clamp(0, 1)
        target = target.detach().cpu().clamp(0, 1)

        ssim_vals = []
        for p, t in zip(pred, target):
            # Ensure the images are on the same device
            p = p.unsqueeze(0).to(self.device)
            t = t.unsqueeze(0).to(self.device)

            # Calculate SSIM using pytorch_msssim
            ssim_val = ssim(p, t, data_range=1.0, size_average=True)
            ssim_vals.append(ssim_val.item())

        return np.mean(ssim_vals)

    def calculate_metrics(self, pred, target):
        """Calculate image metrics: PSNR, SSIM."""
        psnr_val = self.calculate_psnr(pred, target)
        ssim_val = self.calculate_ssim(pred, target)
        return psnr_val, ssim_val
